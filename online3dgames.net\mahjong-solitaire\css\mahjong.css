/* Mahjong Tiles Styles */

.mahjong-tile {
    width: 50px;
    height: 70px;
    background: #fefefe;
    border: 1px solid #ccc;
    border-radius: 8px;
    position: absolute;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
        2px 2px 4px rgba(0,0,0,0.3),
        inset 0 0 0 1px rgba(255,255,255,0.8);
    transform-style: preserve-3d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 42px;
    line-height: 1;
    color: #333;
    overflow: hidden;
    user-select: none;
    z-index: 1;
}

/* Layer-based 3D positioning */
.mahjong-tile[data-layer="0"] {
    box-shadow:
        2px 2px 4px rgba(0,0,0,0.3),
        inset 0 0 0 1px rgba(255,255,255,0.8);
}

.mahjong-tile[data-layer="1"] {
    box-shadow:
        3px 3px 6px rgba(0,0,0,0.4),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        2px 2px 0 rgba(45,90,45,0.6);
}

.mahjong-tile[data-layer="2"] {
    box-shadow:
        4px 4px 8px rgba(0,0,0,0.5),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        3px 3px 0 rgba(45,90,45,0.7);
}

.mahjong-tile[data-layer="3"] {
    box-shadow:
        5px 5px 10px rgba(0,0,0,0.6),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        4px 4px 0 rgba(45,90,45,0.8);
}

.mahjong-tile[data-layer="4"] {
    box-shadow:
        6px 6px 12px rgba(0,0,0,0.7),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        5px 5px 0 rgba(45,90,45,0.9);
}

.mahjong-tile::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: -2px;
    bottom: -2px;
    background: transparent;
    border-radius: 8px;
    z-index: -1;
}

.mahjong-tile::after {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    opacity: 0.6;
    z-index: 1;
    pointer-events: none;
}

.mahjong-tile:hover {
    filter: brightness(1.1);
}

.mahjong-tile[data-layer="0"]:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        3px 3px 6px rgba(0,0,0,0.4),
        inset 0 0 0 1px rgba(255,255,255,0.9);
}

.mahjong-tile[data-layer="1"]:hover {
    transform: translate(-3px, -3px) translateY(-1px) scale(1.02);
    box-shadow:
        6px 6px 10px rgba(0,0,0,0.5),
        inset 0 0 0 1px rgba(255,255,255,0.9),
        3px 3px 0 rgba(45,90,45,0.8);
}

.mahjong-tile[data-layer="2"]:hover {
    transform: translate(-6px, -6px) translateY(-1px) scale(1.02);
    box-shadow:
        9px 9px 14px rgba(0,0,0,0.6),
        inset 0 0 0 1px rgba(255,255,255,0.9),
        6px 6px 0 rgba(45,90,45,0.9);
}

.mahjong-tile[data-layer="3"]:hover {
    transform: translate(-9px, -9px) translateY(-1px) scale(1.02);
    box-shadow:
        12px 12px 18px rgba(0,0,0,0.7),
        inset 0 0 0 1px rgba(255,255,255,0.9),
        9px 9px 0 rgba(45,90,45,1.0);
}

.mahjong-tile[data-layer="4"]:hover {
    transform: translate(-12px, -12px) translateY(-1px) scale(1.02);
    box-shadow:
        15px 15px 22px rgba(0,0,0,0.8),
        inset 0 0 0 1px rgba(255,255,255,0.9),
        12px 12px 0 rgba(45,90,45,1.0);
}

.mahjong-tile.selected {
    background: #fff8dc;
    border-color: #daa520;
    transform: translateY(-2px) scale(1.03);
    box-shadow: 
        0 4px 8px rgba(218, 165, 32, 0.6),
        inset 0 0 0 2px #daa520;
    color: #8b4513;
    z-index: 10;
}

.mahjong-tile.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    filter: grayscale(0.7);
}

.mahjong-tile.hint {
    animation: hintPulse 1.5s ease-in-out infinite;
    z-index: 5;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 
            2px 2px 4px rgba(0,0,0,0.3),
            inset 0 0 0 1px rgba(255,255,255,0.8);
    }
    50% {
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            inset 0 0 0 2px #ffd700;
        transform: translateY(-1px) scale(1.02);
    }
}

.mahjong-tile.removing {
    animation: tileRemove 0.5s ease-in-out forwards;
    z-index: 15;
}

@keyframes tileRemove {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(5deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0) rotate(15deg);
        opacity: 0;
    }
}

.mahjong-tile.appearing {
    animation: tileAppear 0.5s ease-out forwards;
}

@keyframes tileAppear {
    0% {
        transform: scale(0) rotate(-15deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(-5deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* Different types of mahjong tiles */
.tile-wan { 
    color: #d32f2f; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-tong { 
    color: #1976d2; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-tiao { 
    color: #388e3c; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-feng { 
    color: #7b1fa2; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-jian { 
    color: #d32f2f; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-hua { 
    color: #ff6f00; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-season { 
    color: #c2185b; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Special color handling */
.tile-jian[data-value="fa"] {
    filter: hue-rotate(120deg) brightness(1.1) contrast(1.2);
}

.tile-special {
    background: linear-gradient(145deg, #fff8dc 0%, #f0e68c 50%, #daa520 100%);
    border-color: #b8860b;
}

.tile-back {
    background: linear-gradient(145deg, #2d5a2d 0%, #1a4d1a 100%);
    border-color: #1a4d1a;
    color: #ffffff;
    font-size: 32px;
    filter: none;
}

.tile-back::before {
    background: linear-gradient(135deg, #1a4d1a 0%, #0d2d0d 100%) !important;
}

/* Layer system for 3D effect */
.mahjong-tile[data-layer="0"] {
    z-index: 1;
    box-shadow:
        2px 2px 4px rgba(0,0,0,0.3),
        inset 0 0 0 1px rgba(255,255,255,0.8);
}

.mahjong-tile[data-layer="1"] {
    z-index: 10;
    transform: translate(-3px, -3px);
    box-shadow:
        5px 5px 8px rgba(0,0,0,0.4),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        3px 3px 0 rgba(45,90,45,0.8);
}

.mahjong-tile[data-layer="2"] {
    z-index: 20;
    transform: translate(-6px, -6px);
    box-shadow:
        8px 8px 12px rgba(0,0,0,0.5),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        6px 6px 0 rgba(45,90,45,0.9);
}

.mahjong-tile[data-layer="3"] {
    z-index: 30;
    transform: translate(-9px, -9px);
    box-shadow:
        11px 11px 16px rgba(0,0,0,0.6),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        9px 9px 0 rgba(45,90,45,1.0);
}

.mahjong-tile[data-layer="4"] {
    z-index: 40;
    transform: translate(-12px, -12px);
    box-shadow:
        14px 14px 20px rgba(0,0,0,0.7),
        inset 0 0 0 1px rgba(255,255,255,0.8),
        12px 12px 0 rgba(45,90,45,1.0);
}

/* Blocked tiles (not selectable) */
.mahjong-tile.blocked {
    opacity: 0.6;
    cursor: not-allowed;
    filter: brightness(0.7) saturate(0.5);
    position: relative;
}

.mahjong-tile.blocked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    border-radius: 8px;
    z-index: 2;
    pointer-events: none;
}

.mahjong-tile.blocked:hover {
    filter: brightness(0.7) saturate(0.5);
}

/* Selectable tiles indicator */
.mahjong-tile:not(.blocked):not(.selected) {
    position: relative;
}

.mahjong-tile:not(.blocked):not(.selected)::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid rgba(76, 175, 80, 0.4);
    border-radius: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
    pointer-events: none;
}

.mahjong-tile:not(.blocked):not(.selected):hover::after {
    opacity: 1;
    border-color: rgba(76, 175, 80, 0.8);
}

/* Shuffle animation */
.mahjong-layout.shuffling .mahjong-tile {
    animation: shuffle 0.8s ease-in-out;
}

@keyframes shuffle {
    0%, 100% {
        transform: translateX(0) translateY(0) rotate(0deg);
    }
    25% {
        transform: translateX(-10px) translateY(-5px) rotate(-5deg);
    }
    50% {
        transform: translateX(10px) translateY(5px) rotate(5deg);
    }
    75% {
        transform: translateX(-5px) translateY(-10px) rotate(-2deg);
    }
}

/* Match effect */
.mahjong-tile.matching {
    animation: matchEffect 0.3s ease-in-out;
}

@keyframes matchEffect {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 
            0 0 20px rgba(76, 175, 80, 0.8),
            inset 0 0 0 2px #4caf50;
    }
}

/* No moves available effect */
.mahjong-layout.no-moves .mahjong-tile:not(.blocked) {
    animation: noMovesShake 0.5s ease-in-out;
}

@keyframes noMovesShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

/* Responsive tile sizing */
@media (max-width: 768px) {
    .mahjong-tile {
        width: 40px;
        height: 56px;
        font-size: 32px;
    }
    
    .tile-back {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .mahjong-tile {
        width: 35px;
        height: 49px;
        font-size: 28px;
    }
    
    .tile-back {
        font-size: 20px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mahjong-tile {
        box-shadow: 
            1px 1px 2px rgba(0,0,0,0.3),
            inset 0 0 0 0.5px rgba(255,255,255,0.8);
    }
    
    .mahjong-tile::after {
        border-width: 0.5px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .mahjong-tile,
    .mahjong-tile:hover,
    .mahjong-tile.selected {
        transition: none;
        animation: none;
    }
    
    .mahjong-tile.hint {
        animation: none;
        box-shadow: 
            0 0 10px rgba(255, 215, 0, 0.8),
            inset 0 0 0 2px #ffd700;
    }
}

/* Focus styles for keyboard navigation */
.mahjong-tile:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .mahjong-tile {
        box-shadow: none;
        border: 2px solid #000;
        background: #fff;
        color: #000;
    }
}
